import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {
  ChevronDown,
  ChevronUp,
  Leaf,
  Sun,
  Droplets,
  Thermometer,
  MapPin,
  TreePine,
  Flower,
  Calendar,
  AlertTriangle,
  Lightbulb,
  Sprout,
  Heart,
  Shield,
  Zap,
  Activity,
  Scissors,
  Settings,
} from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { Plant, GardenPlant } from '@/types/plant';

interface PlantInfoCardProps {
  plant: Plant | GardenPlant;
  isDiagnosis?: boolean;
}

export const PlantInfoCard: React.FC<PlantInfoCardProps> = ({
  plant,
  isDiagnosis = false,
}) => {
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({});

  const toggleSection = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }));
  };

const renderCareIndicators = () => {
  const { careInstructions } = plant;

  return (
    <View style={styles.careGrid}>
      <View style={styles.careItem}>
        <View style={styles.careIconContainer}>
          <Sun size={20} color={Colors.primary} />
        </View>
        <View style={styles.careContent}>
          <Text style={styles.careLabel}>Light</Text>
          <Text style={styles.careValue}>{careInstructions.light}</Text>
        </View>
      </View>

      <View style={styles.careItem}>
        <View style={styles.careIconContainer}>
          <Droplets size={20} color={Colors.primary} />
        </View>
        <View style={styles.careContent}>
          <Text style={styles.careLabel}>Water</Text>
          <Text style={styles.careValue}>{careInstructions.water}</Text>
        </View>
      </View>

      <View style={styles.careItem}>
        <View style={styles.careIconContainer}>
          <Thermometer size={20} color={Colors.primary} />
        </View>
        <View style={styles.careContent}>
          <Text style={styles.careLabel}>Temperature</Text>
          <Text style={styles.careValue}>
            {careInstructions.temperature.min}°-{careInstructions.temperature.max}°{careInstructions.temperature.unit}
          </Text>
        </View>
      </View>

      {plant.toxicityLevel && (
        <View style={styles.careItem}>
          <View style={styles.careIconContainer}>
            <AlertTriangle size={20} color={Colors.error} />
          </View>
          <View style={styles.careContent}>
            <Text style={styles.careLabel}>Toxicity</Text>
            <Text style={styles.careValue}>{plant.toxicityLevel}</Text>
          </View>
        </View>
      )}

      {plant.growthHabit && (
        <View style={styles.careItem}>
          <View style={styles.careIconContainer}>
            <Activity size={20} color={Colors.primary} />
          </View>
          <View style={styles.careContent}>
            <Text style={styles.careLabel}>Growth Habit</Text>
            <Text style={styles.careValue}>{plant.growthHabit}</Text>
          </View>
        </View>
      )}

      {plant.growthRate && (
        <View style={styles.careItem}>
          <View style={styles.careIconContainer}>
            <Zap size={20} color={Colors.primary} />
          </View>
          <View style={styles.careContent}>
            <Text style={styles.careLabel}>Growth Rate</Text>
            <Text style={styles.careValue}>{plant.growthRate}</Text>
          </View>
        </View>
      )}
    </View>
  );
};
const renderToxicityWarning = () => {
  if (!plant.toxicityLevel || plant.toxicityLevel === 'none') return null;

  const getWarningColor = () => {
    switch (plant.toxicityLevel) {
      case 'mild': return Colors.warning;
      case 'moderate': return '#FF8C00';
      case 'severe': return Colors.error;
      default: return Colors.warning;
    }
  };

  const warningCardStyle = {
    ...styles.toxicityCard,
    borderLeftColor: getWarningColor(),
  };

  return (
    <Card style={warningCardStyle}>
      <View style={styles.toxicityHeader}>
        <AlertTriangle size={20} color={getWarningColor()} />
        <Text style={[styles.toxicityTitle, { color: getWarningColor() }]}>
          Toxicity Warning
        </Text>
      </View>
      <Text style={styles.toxicityText}>
        {plant.toxicityWarning || 'Keep away from pets and children.'}
      </Text>
    </Card>
  );
};
  const renderExpandableSection = (
    title: string,
    content: React.ReactNode,
    sectionKey: string,
    icon: React.ComponentType<any>,
    defaultExpanded: boolean = false
  ) => {
    const isExpanded = expandedSections[sectionKey] ?? defaultExpanded;
    const IconComponent = icon;

    return (
      <Card style={styles.sectionCard}>
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection(sectionKey)}
        >
          <View style={styles.sectionTitleContainer}>
            <IconComponent size={20} color={Colors.primary} />
            <Text style={styles.sectionTitle}>{title}</Text>
          </View>
          {isExpanded ? (
            <ChevronUp size={20} color={Colors.textMuted} />
          ) : (
            <ChevronDown size={20} color={Colors.textMuted} />
          )}
        </TouchableOpacity>
        {isExpanded && <View style={styles.sectionContent}>{content}</View>}
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      {/* Main Plant Information Card - matching /diagnose analysis page layout */}
      <Card style={styles.mainCard}>
        <View style={styles.plantHeader}>
          <Text style={styles.commonName}>{plant.commonName}</Text>
          <Text style={styles.scientificName}>{plant.scientificName}</Text>

          {/* Plant Type Badge */}
          {plant.plantType && (
            <View style={styles.plantTypeContainer}>
              <Sprout size={16} color={Colors.primary} />
              <Text style={styles.plantType}>{plant.plantType}</Text>
            </View>
          )}

          {/* Confidence Score */}
          {plant.confidence !== undefined && (
            <View style={styles.confidenceContainer}>
              <Text style={styles.confidenceLabel}>Confidence: </Text>
              <Text style={styles.confidenceValue}>{Math.round(plant.confidence * 100)}%</Text>
            </View>
          )}
        </View>

        {/* Toxicity Warning */}
        {renderToxicityWarning()}

        {/* Plant Information Section */}
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Plant Information</Text>
          
          <View style={styles.infoGrid}>
{plant.nativeRegion && (
  <View style={styles.infoRow}>
    <MapPin size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Native Region: </Text>
    <Text style={styles.infoValue}>{plant.nativeRegion}</Text>
  </View>
)}

{plant.growthHabit && (
  <View style={styles.infoRow}>
    <Activity size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Growth Habit: </Text>
    <Text style={styles.infoValue}>{plant.growthHabit}</Text>
  </View>
)}

{plant.growthRate && (
  <View style={styles.infoRow}>
    <Zap size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Growth Rate: </Text>
    <Text style={styles.infoValue}>{plant.growthRate}</Text>
  </View>
)}

{plant.matureHeight && (
  <View style={styles.infoRow}>
    <Leaf size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Mature Height: </Text>
    <Text style={styles.infoValue}>{plant.matureHeight}</Text>
  </View>
)}

{plant.matureWidth && (
  <View style={styles.infoRow}>
    <Leaf size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Mature Width: </Text>
    <Text style={styles.infoValue}>{plant.matureWidth}</Text>
  </View>
)}

{plant.bloomTime && (
  <View style={styles.infoRow}>
    <Flower size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Bloom Time: </Text>
    <Text style={styles.infoValue}>{plant.bloomTime}</Text>
  </View>
)}

{plant.flowerColors && Array.isArray(plant.flowerColors) && plant.flowerColors.length > 0 && (
  <View style={styles.infoRow}>
    <Flower size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Flower Colors: </Text>
    <Text style={styles.infoValue}>{plant.flowerColors.join(', ')}</Text>
  </View>
)}

{plant.foliageType && (
  <View style={styles.infoRow}>
    <Leaf size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Foliage Type: </Text>
    <Text style={styles.infoValue}>{plant.foliageType}</Text>
  </View>
)}

{plant.hardinessZones && (
  <View style={styles.infoRow}>
    <Thermometer size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Hardiness Zones: </Text>
    <Text style={styles.infoValue}>{plant.hardinessZones}</Text>
  </View>
)}

{plant.minTemperature && (
  <View style={styles.infoRow}>
    <Thermometer size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Min Temperature: </Text>
    <Text style={styles.infoValue}>{plant.minTemperature}</Text>
  </View>
)}
            {plant.growthHabit && (
              <View style={styles.infoRow}>
                <Activity size={16} color={Colors.primary} />
                <Text style={styles.infoLabel}>Growth Habit: </Text>
                <Text style={styles.infoValue}>{plant.growthHabit}</Text>
              </View>
            )}

            {plant.growthRate && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Growth Rate: </Text>
                <Text style={styles.infoValue}>{plant.growthRate}</Text>
              </View>
            )}
          </View>
        </View>

        {/* Care Instructions Section */}
        <View style={styles.careSection}>
          <Text style={styles.sectionTitle}>Care Instructions</Text>
          
          {/* Care Icons Grid */}
          <View style={styles.careIconsGrid}>
            <View style={styles.careIconItem}>
              <Sun size={24} color={Colors.primary} />
              <Text style={styles.careIconLabel}>Light</Text>
              <Text style={styles.careIconValue}>{plant.careInstructions.light}</Text>
            </View>

            <View style={styles.careIconItem}>
              <Droplets size={24} color={Colors.primary} />
              <Text style={styles.careIconLabel}>Water</Text>
              <Text style={styles.careIconValue}>{plant.careInstructions.water}</Text>
            </View>

            <View style={styles.careIconItem}>
              <Thermometer size={24} color={Colors.primary} />
              <Text style={styles.careIconLabel}>Temperature</Text>
              <Text style={styles.careIconValue}>
                {plant.careInstructions.temperature.min}°-{plant.careInstructions.temperature.max}°{plant.careInstructions.temperature.unit}
              </Text>
            </View>
          </View>

          {/* Detailed Care Instructions */}
          <View style={styles.careDetails}>
            {plant.careInstructions.soil && (
              <View style={styles.careDetailRow}>
                <Text style={styles.careDetailLabel}>Soil:</Text>
                <Text style={styles.careDetailValue}>
                  {plant.careInstructions.soil}
                </Text>
              </View>
            )}

            {plant.careInstructions.fertilizer && (
              <View style={styles.careDetailRow}>
                <Text style={styles.careDetailLabel}>Fertilizer:</Text>
                <Text style={styles.careDetailValue}>
                  {plant.careInstructions.fertilizer}
                </Text>
              </View>
            )}

            {plant.careInstructions.humidity && (
              <View style={styles.careDetailRow}>
                <Text style={styles.careDetailLabel}>Humidity:</Text>
                <Text style={styles.careDetailValue}>{plant.careInstructions.humidity}</Text>
              </View>
            )}
          </View>
        </View>

        {/* About This Plant Section */}
        {plant.description && (
          <View style={styles.aboutSection}>
            <Text style={styles.sectionTitle}>About This Plant</Text>
            <Text style={styles.description}>
              {plant.description}
            </Text>
          </View>
        )}

        {/* Tags Section */}
        {plant.tags && Array.isArray(plant.tags) && plant.tags.length > 0 && (
          <View style={styles.tagsSection}>
            <Text style={styles.sectionTitle}>Tags</Text>
            <View style={styles.tagsContainer}>
              {plant.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Expandable Sections for Identified Plants */}
        {!isDiagnosis && (
          <>
{renderExpandableSection(
  "Plant Characteristics",
  <View>
    {plant.matureDescription && (
      <Text style={styles.expandableText}>• {plant.matureDescription}</Text>
    )}
    {plant.foliageType && (
      <Text style={styles.expandableText}>• {plant.foliageType}</Text>
    )}
    {plant.flowerColors && Array.isArray(plant.flowerColors) && plant.flowerColors.length > 0 && (
      <Text style={styles.expandableText}>• Flowers in {plant.flowerColors.join(', ')} colors</Text>
    )}
    {plant.bloomTime && (
      <Text style={styles.expandableText}>• Blooms in {plant.bloomTime}</Text>
    )}
    {plant.growthHabit && (
      <Text style={styles.expandableText}>• Growth habit: {plant.growthHabit}</Text>
    )}
    {plant.growthRate && (
      <Text style={styles.expandableText}>• Growth rate: {plant.growthRate}</Text>
    )}
    {plant.matureHeight && (
      <Text style={styles.expandableText}>• Mature height: {plant.matureHeight}</Text>
    )}
    {plant.matureWidth && (
      <Text style={styles.expandableText}>• Mature width: {plant.matureWidth}</Text>
    )}
  </View>,
  "characteristics",
  Leaf
)}
{plant.seasonalCare && renderExpandableSection(
  "Seasonal Care",
  <View>
    <Text style={styles.expandableText}>{plant.seasonalCare}</Text>
  </View>,
  "seasonalCare",
  Calendar
)}

{plant.propagation && renderExpandableSection(
  "Propagation",
  <View>
    <Text style={styles.expandableText}>{plant.propagation}</Text>
  </View>,
  "propagation",
  Scissors
)}
{plant.pestsAndDiseases && renderExpandableSection(
  "Pests & Diseases",
  <View>
    <Text style={styles.expandableText}>{plant.pestsAndDiseases}</Text>
  </View>,
  "pestsAndDiseases",
  Shield
)}
{plant.uses && Array.isArray(plant.uses) && plant.uses.length > 0 && renderExpandableSection(
  "Uses & Benefits",
  <View>
    {plant.uses.map((use, index) => (
      <Text key={index} style={styles.expandableText}>• {use}</Text>
    ))}
  </View>,
  "uses",
  Heart
)}

{plant.funFacts && Array.isArray(plant.funFacts) && plant.funFacts.length > 0 && renderExpandableSection(
  "Fun Facts",
  <View>
    {plant.funFacts.map((fact, index) => (
      <Text key={index} style={styles.expandableText}>• {fact}</Text>
    ))}
  </View>,
  "funFacts",
  Lightbulb
)}

{plant.companionPlants && Array.isArray(plant.companionPlants) && plant.companionPlants.length > 0 && renderExpandableSection(
  "Companion Plants",
  <View>
    {plant.companionPlants.map((companion, index) => (
      <Text key={index} style={styles.expandableText}>• {companion}</Text>
    ))}
  </View>,
  "companionPlants",
  Flower
)}

{plant.maintenanceLevel && renderExpandableSection(
  "Maintenance Level",
  <View>
    <Text style={styles.expandableText}>{plant.maintenanceLevel}</Text>
  </View>,
  "maintenanceLevel",
  Settings
)}
          </>
        )}
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainCard: {
    marginBottom: 16,
  },
  plantHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  commonName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 18,
    fontStyle: 'italic',
    color: Colors.textMuted,
    textAlign: 'center',
    marginBottom: 8,
  },
  plantTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginTop: 8,
  },
  plantType: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
    marginLeft: 4,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  confidenceLabel: {
    fontSize: 14,
    color: Colors.textMuted,
  },
  confidenceValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary,
  },
  toxicityCard: {
    backgroundColor: Colors.errorLight,
    borderLeftWidth: 4,
    borderLeftColor: Colors.error,
    marginBottom: 16,
  },
  toxicityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  toxicityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.error,
    marginLeft: 8,
  },
  toxicityText: {
    fontSize: 14,
    color: Colors.error,
    lineHeight: 20,
  },
  // New styles for the updated layout
  infoSection: {
    marginBottom: 16,
  },
  infoGrid: {
    gap: 8,
  },
  careSection: {
    marginBottom: 16,
  },
  careIconsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingVertical: 12,
  },
  careIconItem: {
    alignItems: 'center',
    flex: 1,
  },
  careIconLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text,
    marginTop: 4,
  },
  careIconValue: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 2,
  },
  careDetailLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  careDetailValue: {
    fontSize: 14,
    color: Colors.textMuted,
    lineHeight: 20,
    flex: 1,
  },
  aboutSection: {
    marginBottom: 16,
  },
  tagsSection: {
    marginBottom: 16,
  },
  // Existing styles
  infoCard: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textMuted,
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    color: Colors.text,
    flex: 2,
  },
  careCard: {
    marginBottom: 16,
  },
  careGrid: {
    gap: 12,
    marginBottom: 16,
  },
  careItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.secondary,
    padding: 12,
    borderRadius: 12,
  },
  careIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  careContent: {
    flex: 1,
  },
  careLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  careValue: {
    fontSize: 14,
    color: Colors.textMuted,
    textTransform: 'capitalize',
  },
  careDetails: {
    gap: 8,
  },
  careDetailRow: {
    marginBottom: 12,
  },
  descriptionCard: {
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
  },
  tagsCard: {
    marginBottom: 16,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
  },
  sectionCard: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionContent: {
    marginTop: 12,
  },
  expandableText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    marginBottom: 4,
  },
});
